import { Request, Response } from "express";
import { CreateUserDto, UpdateUserDto } from "../models/user.model";
import * as userService from "../services/user.service";

export const registerUser = async (
	req: Request,
	res: Response
): Promise<void> => {
	try {
		const user = req.user;
		const userData: CreateUserDto = {
			appwriteId: user.id,
			email: user.email,
			name: req.body.name || "",
			profile: req.body.profile || {},
			userType: req.user.type,
		};

		// Check if user is already registered
		const existingUser = await userService.findByAppwriteId(user.id);
		if (existingUser) {
			res.status(409).json({ error: "User already registered" });
			return;
		}
		// frontend--> sdk --> frontend(token) --> backend --> db

		const newUser = await userService.createUser(userData);
		res.status(201).json(newUser);
	} catch (error: any) {
		console.error("Error registering user:", error);
		res.status(500).json({ error: error.message || "Failed to register user" });
	}
};

export const getUserProfile = async (
	req: Request,
	res: Response
): Promise<void> => {
	try {
		const appwriteUser = (req as any).user;
		const user = await userService.findByAppwriteId(appwriteUser.$id);

		if (!user) {
			res.status(404).json({ error: "User not found" });
			return;
		}

		res.status(200).json(user);
	} catch (error: any) {
		console.error("Error fetching user profile:", error);
		res.status(500).json({ error: "Failed to fetch user profile" });
	}
};

export const updateUserProfile = async (
	req: Request,
	res: Response
): Promise<void> => {
	try {
		const appwriteUser = (req as any).user;
		const updateData: UpdateUserDto = req.body;

		// Validate update data
		if (Object.keys(updateData).length === 0) {
			res.status(400).json({ error: "No update data provided" });
			return;
		}

		const updatedUser = await userService.updateUser(
			appwriteUser.$id,
			updateData
		);

		if (!updatedUser) {
			res.status(404).json({ error: "User not found" });
			return;
		}

		res.status(200).json(updatedUser);
	} catch (error: any) {
		console.error("Error updating user profile:", error);
		res.status(500).json({ error: "Failed to update user profile" });
	}
};

export const deleteUserProfile = async (
	req: Request,
	res: Response
): Promise<void> => {
	try {
		const appwriteUser = (req as any).user;
		const success = await userService.deleteUser(appwriteUser.$id);

		if (!success) {
			res.status(404).json({ error: "User not found" });
			return;
		}

		res.status(200).json({ message: "User deleted successfully" });
	} catch (error: any) {
		console.error("Error deleting user:", error);
		res.status(500).json({ error: "Failed to delete user" });
	}
};
