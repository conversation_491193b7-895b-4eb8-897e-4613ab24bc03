import { Collection, ObjectId } from "mongodb";
import { db } from "../config/db";
import { CreateUserDto, UpdateUserDto, User } from "../models/user.model";

const userCollection = (): Collection<User> => db.collection<User>("users");

export const createUser = async (userData: CreateUserDto): Promise<User> => {
	// Check if user with this appwriteId already exists
	const existingUser = await findByAppwriteId(userData.appwriteId);
	if (existingUser) {
		throw new Error("User already exists");
	}

	const newUser: User = {
		...userData,
		userType: userData.userType as "seeker" | "provider",
		createdAt: new Date(),
		updatedAt: new Date(),
		isDeleted: false,
	};

	const result = await userCollection().insertOne(newUser);
	return { ...newUser, _id: result.insertedId.toString() };
};

export const findByAppwriteId = async (
	appwriteId: string
): Promise<User | null> => {
	return await userCollection().findOne({
		appwriteId,
		isDeleted: false,
	});
};

export const findById = async (userId: string): Promise<User | null> => {
	if (!ObjectId.isValid(userId)) {
		return null;
	}
	return await userCollection().findOne({
		_id: new ObjectId(userId),
		isDeleted: false,
	});
};

export const updateUser = async (
	appwriteId: string,
	updateData: UpdateUserDto
): Promise<User | null> => {
	const user = await findByAppwriteId(appwriteId);
	if (!user) {
		return null;
	}

	const updatedUser = {
		$set: {
			...updateData,
			updatedAt: new Date(),
		},
	};

	await userCollection().updateOne({ appwriteId }, updatedUser);

	return await findByAppwriteId(appwriteId);
};

export const deleteUser = async (appwriteId: string): Promise<boolean> => {
	const user = await findByAppwriteId(appwriteId);
	if (!user) {
		return false;
	}

	const result = await userCollection().updateOne(
		{ appwriteId },
		{ $set: { isDeleted: true, updatedAt: new Date() } }
	);

	return result.modifiedCount > 0;
};
